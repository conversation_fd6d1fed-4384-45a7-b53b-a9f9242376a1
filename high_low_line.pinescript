//@version=6
indicator("高低点连线图", shorttitle="高低点连线", overlay=true, max_lines_count=20)

// ===========================
// 参数设置
// ===========================

pivot_length = input.int(5, title="枢轴长度", minval=1, maxval=20, tooltip="用于识别高低点的左右K线数量")

var g1 = "=== 高点设置 ==="
high_line_color = input.color(color.red, title="高点连线颜色", group=g1)
show_high_lines = input.bool(true, title="显示高点连线", group=g1)
show_high_labels = input.bool(true, title="显示高点标签", group=g1)

var g2 = "=== 低点设置 ==="
low_line_color = input.color(color.green, title="低点连线颜色", group=g2)
show_low_lines = input.bool(true, title="显示低点连线", group=g2)
show_low_labels = input.bool(true, title="显示低点标签", group=g2)

var g3 = "=== 连线设置 ==="
line_width = input.int(2, title="连线宽度", minval=1, maxval=5, group=g3)
line_style = input.string("实线", title="连线样式", options=["实线", "虚线", "点线"], group=g3)
extend_line = input.bool(true, title="延伸最新连线", tooltip="向右延伸最新的连线", group=g3)

// ===========================
// 高低点识别
// ===========================

// 识别枢轴高点和低点
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)
pivot_low = ta.pivotlow(low, pivot_length, pivot_length)

// ===========================
// 数据结构
// ===========================

// 高点数据结构
type HighPoint
    int bar_index
    float price

// 低点数据结构
type LowPoint
    int bar_index
    float price

// 常量定义
MAX_POINTS = 8
MAX_LINES = 7

// 使用数组存储高低点
var array<HighPoint> high_points = array.new<HighPoint>()
var array<LowPoint> low_points = array.new<LowPoint>()

// 使用数组存储连线对象
var array<line> high_lines = array.new<line>()
var array<line> low_lines = array.new<line>()

// 存储标签对象
var array<label> high_labels = array.new<label>()
var array<label> low_labels = array.new<label>()

// ===========================
// 辅助函数
// ===========================

// 确定连线样式
line_style_type = switch line_style
    "虚线" => line.style_dashed
    "点线" => line.style_dotted
    => line.style_solid

// 确定延伸方式（只有最新连线延伸）
extend_type = extend_line ? extend.right : extend.none

// 透明度数组（用于渐变效果）
transparency_levels = array.from(0, 15, 30, 45, 60, 75, 85, 90)

// 删除所有连线的函数
delete_all_lines(lines_array) =>
    if array.size(lines_array) > 0
        for i = 0 to array.size(lines_array) - 1
            line_obj = array.get(lines_array, i)
            if not na(line_obj)
                line.delete(line_obj)
        array.clear(lines_array)

// 删除所有标签的函数
delete_all_labels(labels_array) =>
    if array.size(labels_array) > 0
        for i = 0 to array.size(labels_array) - 1
            label.delete(array.get(labels_array, i))
        array.clear(labels_array)

// 添加新点到数组的函数
add_point_to_array(points_array, new_point) =>
    array.unshift(points_array, new_point)
    if array.size(points_array) > MAX_POINTS
        array.pop(points_array)

// 创建连线的函数
create_lines(points_array, lines_array, line_color, is_high) =>
    delete_all_lines(lines_array)

    points_size = array.size(points_array)
    if points_size >= 2
        for i = 0 to math.min(points_size - 2, MAX_LINES - 1)
            point1 = array.get(points_array, i)
            point2 = array.get(points_array, i + 1)

            if not na(point1) and not na(point2)
                // 第一条线（最新）使用用户设置的样式和延伸
                if i == 0
                    line_obj = line.new(
                         x1=point2.bar_index, y1=point2.price,
                         x2=point1.bar_index, y2=point1.price,
                         color=line_color,
                         width=line_width,
                         style=line_style_type,
                         extend=extend_type)
                else
                    // 其他线使用渐变透明度和固定样式
                    transparency = array.get(transparency_levels, i)
                    line_obj = line.new(
                         x1=point2.bar_index, y1=point2.price,
                         x2=point1.bar_index, y2=point1.price,
                         color=color.new(line_color, transparency),
                         width=math.max(1, line_width-1),
                         style=line.style_solid,
                         extend=extend.none)

                array.push(lines_array, line_obj)

// 创建标签的函数
create_labels(points_array, labels_array, line_color, is_high) =>
    delete_all_labels(labels_array)

    points_size = array.size(points_array)
    if points_size > 0
        for i = 0 to math.min(points_size - 1, MAX_POINTS - 1)
            point = array.get(points_array, i)
            if not na(point)
                transparency = array.get(transparency_levels, i)
                label_color = i == 0 ? line_color : color.new(line_color, transparency)

                prefix = is_high ? "H" : "L"
                label_text = prefix + str.tostring(i + 1) + ": " + str.tostring(point.price, "#.##")
                label_style = is_high ? label.style_label_down : label.style_label_up

                new_label = label.new(
                     point.bar_index, point.price,
                     text=label_text,
                     style=label_style,
                     color=label_color,
                     textcolor=color.white,
                     size=size.small)

                array.push(labels_array, new_label)

// ===========================
// 检测新高点并更新
// ===========================

if not na(pivot_high)
    // 获取实际高点的K线索引和价格
    high_bar_index = bar_index - pivot_length
    high_price = high[pivot_length]  // 获取实际K线的最高价

    // 创建新的高点（使用K线的实际最高价）
    new_high = HighPoint.new(high_bar_index, high_price)

    // 添加新高点到数组
    add_point_to_array(high_points, new_high)

    // 重新绘制高点连线
    if show_high_lines
        create_lines(high_points, high_lines, high_line_color, true)

    // 添加所有高点标签
    if show_high_labels
        create_labels(high_points, high_labels, high_line_color, true)

// ===========================
// 检测新低点并更新
// ===========================

if not na(pivot_low)
    // 获取实际低点的K线索引和价格
    low_bar_index = bar_index - pivot_length
    low_price = low[pivot_length]  // 获取实际K线的最低价

    // 创建新的低点（使用K线的实际最低价）
    new_low = LowPoint.new(low_bar_index, low_price)

    // 添加新低点到数组
    add_point_to_array(low_points, new_low)

    // 重新绘制低点连线
    if show_low_lines
        create_lines(low_points, low_lines, low_line_color, false)

    // 添加所有低点标签
    if show_low_labels
        create_labels(low_points, low_labels, low_line_color, false)

// ===========================
// 信息面板
// ===========================

var table info_table = table.new(position.top_right, 4, 9, bgcolor=color.new(color.white, 20), border_width=1)

if barstate.islast
    table.clear(info_table, 0, 0, 3, 8)

    table.cell(info_table, 0, 0, "八个高低点连线图", text_color=color.black, bgcolor=color.new(color.gray, 50), text_size=size.small)
    table.cell(info_table, 1, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))
    table.cell(info_table, 2, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))
    table.cell(info_table, 3, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))

    // 显示高点
    high_points_size = array.size(high_points)
    if high_points_size > 0
        for i = 0 to math.min(high_points_size - 1, MAX_POINTS - 1)
            point = array.get(high_points, i)
            if not na(point)
                transparency = array.get(transparency_levels, i)
                point_color = i == 0 ? high_line_color : color.new(high_line_color, transparency)

                table.cell(info_table, 0, i + 1, "H" + str.tostring(i + 1), text_color=color.black, text_size=size.small)
                table.cell(info_table, 1, i + 1, str.tostring(point.price, "#.##"), text_color=point_color, text_size=size.small)

    // 显示低点
    low_points_size = array.size(low_points)
    if low_points_size > 0
        for i = 0 to math.min(low_points_size - 1, MAX_POINTS - 1)
            point = array.get(low_points, i)
            if not na(point)
                transparency = array.get(transparency_levels, i)
                point_color = i == 0 ? low_line_color : color.new(low_line_color, transparency)

                table.cell(info_table, 2, i + 1, "L" + str.tostring(i + 1), text_color=color.black, text_size=size.small)
                table.cell(info_table, 3, i + 1, str.tostring(point.price, "#.##"), text_color=point_color, text_size=size.small)

// ===========================
// 警报功能
// ===========================

alertcondition(not na(pivot_high), title="新高点检测", message="检测到新的高点，高点连线已更新")
alertcondition(not na(pivot_low), title="新低点检测", message="检测到新的低点，低点连线已更新")
alertcondition(not na(pivot_high) and array.size(high_points) >= MAX_POINTS, title="八个高点完整", message="已收集到八个高点，高点连线图案完整")
alertcondition(not na(pivot_low) and array.size(low_points) >= MAX_POINTS, title="八个低点完整", message="已收集到八个低点，低点连线图案完整")

// 趋势变化警报
if array.size(high_points) >= 3
    high1 = array.get(high_points, 0)
    high2 = array.get(high_points, 1)
    high3 = array.get(high_points, 2)

    if not na(high1) and not na(high2) and not na(high3)
        if high1.bar_index != high2.bar_index and high2.bar_index != high3.bar_index
            current_slope = (high1.price - high2.price) / (high1.bar_index - high2.bar_index)
            previous_slope = (high2.price - high3.price) / (high2.bar_index - high3.bar_index)

            // 检测趋势转换
            trend_change = (current_slope > 0 and previous_slope < 0) or (current_slope < 0 and previous_slope > 0)
            if trend_change
                alert("高点趋势发生转换", alert.freq_once_per_bar)

if array.size(low_points) >= 3
    low1 = array.get(low_points, 0)
    low2 = array.get(low_points, 1)
    low3 = array.get(low_points, 2)

    if not na(low1) and not na(low2) and not na(low3)
        if low1.bar_index != low2.bar_index and low2.bar_index != low3.bar_index
            current_slope = (low1.price - low2.price) / (low1.bar_index - low2.bar_index)
            previous_slope = (low2.price - low3.price) / (low2.bar_index - low3.bar_index)

            // 检测趋势转换
            trend_change = (current_slope > 0 and previous_slope < 0) or (current_slope < 0 and previous_slope > 0)
            if trend_change
                alert("低点趋势发生转换", alert.freq_once_per_bar)