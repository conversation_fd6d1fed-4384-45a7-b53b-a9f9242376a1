//@version=6
indicator("高低点连线图", shorttitle="高低点连线", overlay=true, max_lines_count=20)

// ===========================
// 参数设置
// ===========================

pivot_length = input.int(5, title="枢轴长度", minval=1, maxval=20, tooltip="用于识别高低点的左右K线数量")

var g1 = "=== 高点设置 ==="
high_line_color = input.color(color.red, title="高点连线颜色", group=g1)
show_high_lines = input.bool(true, title="显示高点连线", group=g1)
show_high_labels = input.bool(true, title="显示高点标签", group=g1)

var g2 = "=== 低点设置 ==="
low_line_color = input.color(color.green, title="低点连线颜色", group=g2)
show_low_lines = input.bool(true, title="显示低点连线", group=g2)
show_low_labels = input.bool(true, title="显示低点标签", group=g2)

var g3 = "=== 连线设置 ==="
line_width = input.int(2, title="连线宽度", minval=1, maxval=5, group=g3)
line_style = input.string("实线", title="连线样式", options=["实线", "虚线", "点线"], group=g3)
extend_line = input.bool(true, title="延伸最新连线", tooltip="向右延伸最新的连线", group=g3)

// ===========================
// 高低点识别
// ===========================

// 识别枢轴高点和低点
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)
pivot_low = ta.pivotlow(low, pivot_length, pivot_length)

// ===========================
// 数据结构
// ===========================

// 高点数据结构
type HighPoint
    int bar_index
    float price

// 低点数据结构
type LowPoint
    int bar_index
    float price

// 存储最近的八个高点
var HighPoint high1 = na  // 最新高点
var HighPoint high2 = na  // 第二新高点
var HighPoint high3 = na  // 第三新高点
var HighPoint high4 = na  // 第四新高点
var HighPoint high5 = na  // 第五新高点
var HighPoint high6 = na  // 第六新高点
var HighPoint high7 = na  // 第七新高点
var HighPoint high8 = na  // 第八新高点

// 存储最近的八个低点
var LowPoint low1 = na   // 最新低点
var LowPoint low2 = na   // 第二新低点
var LowPoint low3 = na   // 第三新低点
var LowPoint low4 = na   // 第四新低点
var LowPoint low5 = na   // 第五新低点
var LowPoint low6 = na   // 第六新低点
var LowPoint low7 = na   // 第七新低点
var LowPoint low8 = na   // 第八新低点

// 存储高点连线对象
var line high_line1 = na  // high1 到 high2 的连线
var line high_line2 = na  // high2 到 high3 的连线
var line high_line3 = na  // high3 到 high4 的连线
var line high_line4 = na  // high4 到 high5 的连线
var line high_line5 = na  // high5 到 high6 的连线
var line high_line6 = na  // high6 到 high7 的连线
var line high_line7 = na  // high7 到 high8 的连线

// 存储低点连线对象
var line low_line1 = na   // low1 到 low2 的连线
var line low_line2 = na   // low2 到 low3 的连线
var line low_line3 = na   // low3 到 low4 的连线
var line low_line4 = na   // low4 到 low5 的连线
var line low_line5 = na   // low5 到 low6 的连线
var line low_line6 = na   // low6 到 low7 的连线
var line low_line7 = na   // low7 到 low8 的连线

// 存储标签对象
var array<label> high_labels = array.new<label>()
var array<label> low_labels = array.new<label>()

// ===========================
// 连线绘制逻辑
// ===========================

// 确定连线样式
line_style_type = switch line_style
    "虚线" => line.style_dashed
    "点线" => line.style_dotted
    => line.style_solid

// 确定延伸方式（只有最新连线延伸）
extend_type = extend_line ? extend.right : extend.none

// ===========================
// 检测新高点并更新
// ===========================

if not na(pivot_high)
    // 获取实际高点的K线索引和价格
    high_bar_index = bar_index - pivot_length
    high_price = high[pivot_length]  // 获取实际K线的最高价

    // 创建新的高点（使用K线的实际最高价）
    new_high = HighPoint.new(high_bar_index, high_price)

    // 更新高点序列（向后推移）
    high8 := high7
    high7 := high6
    high6 := high5
    high5 := high4
    high4 := high3
    high3 := high2
    high2 := high1
    high1 := new_high

    // 删除所有旧的高点连线
    if not na(high_line1)
        line.delete(high_line1)
        high_line1 := na
    if not na(high_line2)
        line.delete(high_line2)
        high_line2 := na
    if not na(high_line3)
        line.delete(high_line3)
        high_line3 := na
    if not na(high_line4)
        line.delete(high_line4)
        high_line4 := na
    if not na(high_line5)
        line.delete(high_line5)
        high_line5 := na
    if not na(high_line6)
        line.delete(high_line6)
        high_line6 := na
    if not na(high_line7)
        line.delete(high_line7)
        high_line7 := na

    // 删除所有旧的高点标签
    if array.size(high_labels) > 0
        for i = 0 to array.size(high_labels) - 1
            label.delete(array.get(high_labels, i))
        array.clear(high_labels)

    // 重新绘制高点连线
    if show_high_lines
        // 创建高点连线：连接相邻的高点
        if not na(high1) and not na(high2)
            // 最新连线：high1 到 high2（可延伸）
            high_line1 := line.new(x1=high2.bar_index, y1=high2.price, x2=high1.bar_index, y2=high1.price, color=high_line_color, width=line_width, style=line_style_type, extend=extend_type)

        if not na(high2) and not na(high3)
            // 第二条连线：high2 到 high3（不延伸）
            high_line2 := line.new(x1=high3.bar_index, y1=high3.price, x2=high2.bar_index, y2=high2.price, color=color.new(high_line_color, 15), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(high3) and not na(high4)
            // 第三条连线：high3 到 high4（不延伸）
            high_line3 := line.new(x1=high4.bar_index, y1=high4.price, x2=high3.bar_index, y2=high3.price, color=color.new(high_line_color, 30), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(high4) and not na(high5)
            // 第四条连线：high4 到 high5（不延伸）
            high_line4 := line.new(x1=high5.bar_index, y1=high5.price, x2=high4.bar_index, y2=high4.price, color=color.new(high_line_color, 45), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(high5) and not na(high6)
            // 第五条连线：high5 到 high6（不延伸）
            high_line5 := line.new(x1=high6.bar_index, y1=high6.price, x2=high5.bar_index, y2=high5.price, color=color.new(high_line_color, 60), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(high6) and not na(high7)
            // 第六条连线：high6 到 high7（不延伸）
            high_line6 := line.new(x1=high7.bar_index, y1=high7.price, x2=high6.bar_index, y2=high6.price, color=color.new(high_line_color, 75), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(high7) and not na(high8)
            // 第七条连线：high7 到 high8（不延伸）
            high_line7 := line.new(x1=high8.bar_index, y1=high8.price, x2=high7.bar_index, y2=high7.price, color=color.new(high_line_color, 85), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

    // 添加所有高点标签
    if show_high_labels
        // 添加最新高点标签
        label_text = "H1: " + str.tostring(high_price, "#.##")
        new_label = label.new(high_bar_index, high_price, text=label_text, style=label.style_label_down, color=high_line_color, textcolor=color.white, size=size.small)
        array.push(high_labels, new_label)

        // 添加其他高点标签
        if not na(high2)
            label_text2 = "H2: " + str.tostring(high2.price, "#.##")
            label2 = label.new(high2.bar_index, high2.price, text=label_text2, style=label.style_label_down, color=color.new(high_line_color, 15), textcolor=color.white, size=size.small)
            array.push(high_labels, label2)

        if not na(high3)
            label_text3 = "H3: " + str.tostring(high3.price, "#.##")
            label3 = label.new(high3.bar_index, high3.price, text=label_text3, style=label.style_label_down, color=color.new(high_line_color, 30), textcolor=color.white, size=size.small)
            array.push(high_labels, label3)

        if not na(high4)
            label_text4 = "H4: " + str.tostring(high4.price, "#.##")
            label4 = label.new(high4.bar_index, high4.price, text=label_text4, style=label.style_label_down, color=color.new(high_line_color, 45), textcolor=color.white, size=size.small)
            array.push(high_labels, label4)

        if not na(high5)
            label_text5 = "H5: " + str.tostring(high5.price, "#.##")
            label5 = label.new(high5.bar_index, high5.price, text=label_text5, style=label.style_label_down, color=color.new(high_line_color, 60), textcolor=color.white, size=size.small)
            array.push(high_labels, label5)

        if not na(high6)
            label_text6 = "H6: " + str.tostring(high6.price, "#.##")
            label6 = label.new(high6.bar_index, high6.price, text=label_text6, style=label.style_label_down, color=color.new(high_line_color, 75), textcolor=color.white, size=size.small)
            array.push(high_labels, label6)

        if not na(high7)
            label_text7 = "H7: " + str.tostring(high7.price, "#.##")
            label7 = label.new(high7.bar_index, high7.price, text=label_text7, style=label.style_label_down, color=color.new(high_line_color, 85), textcolor=color.white, size=size.small)
            array.push(high_labels, label7)

        if not na(high8)
            label_text8 = "H8: " + str.tostring(high8.price, "#.##")
            label8 = label.new(high8.bar_index, high8.price, text=label_text8, style=label.style_label_down, color=color.new(high_line_color, 90), textcolor=color.white, size=size.small)
            array.push(high_labels, label8)

// ===========================
// 检测新低点并更新
// ===========================

if not na(pivot_low)
    // 获取实际低点的K线索引和价格
    low_bar_index = bar_index - pivot_length
    low_price = low[pivot_length]  // 获取实际K线的最低价

    // 创建新的低点（使用K线的实际最低价）
    new_low = LowPoint.new(low_bar_index, low_price)

    // 更新低点序列（向后推移）
    low8 := low7
    low7 := low6
    low6 := low5
    low5 := low4
    low4 := low3
    low3 := low2
    low2 := low1
    low1 := new_low

    // 删除所有旧的低点连线
    if not na(low_line1)
        line.delete(low_line1)
        low_line1 := na
    if not na(low_line2)
        line.delete(low_line2)
        low_line2 := na
    if not na(low_line3)
        line.delete(low_line3)
        low_line3 := na
    if not na(low_line4)
        line.delete(low_line4)
        low_line4 := na
    if not na(low_line5)
        line.delete(low_line5)
        low_line5 := na
    if not na(low_line6)
        line.delete(low_line6)
        low_line6 := na
    if not na(low_line7)
        line.delete(low_line7)
        low_line7 := na

    // 删除所有旧的低点标签
    if array.size(low_labels) > 0
        for i = 0 to array.size(low_labels) - 1
            label.delete(array.get(low_labels, i))
        array.clear(low_labels)

    // 重新绘制低点连线
    if show_low_lines
        // 创建低点连线：连接相邻的低点
        if not na(low1) and not na(low2)
            // 最新连线：low1 到 low2（可延伸）
            low_line1 := line.new(x1=low2.bar_index, y1=low2.price, x2=low1.bar_index, y2=low1.price, color=low_line_color, width=line_width, style=line_style_type, extend=extend_type)

        if not na(low2) and not na(low3)
            // 第二条连线：low2 到 low3（不延伸）
            low_line2 := line.new(x1=low3.bar_index, y1=low3.price, x2=low2.bar_index, y2=low2.price, color=color.new(low_line_color, 15), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(low3) and not na(low4)
            // 第三条连线：low3 到 low4（不延伸）
            low_line3 := line.new(x1=low4.bar_index, y1=low4.price, x2=low3.bar_index, y2=low3.price, color=color.new(low_line_color, 30), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(low4) and not na(low5)
            // 第四条连线：low4 到 low5（不延伸）
            low_line4 := line.new(x1=low5.bar_index, y1=low5.price, x2=low4.bar_index, y2=low4.price, color=color.new(low_line_color, 45), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(low5) and not na(low6)
            // 第五条连线：low5 到 low6（不延伸）
            low_line5 := line.new(x1=low6.bar_index, y1=low6.price, x2=low5.bar_index, y2=low5.price, color=color.new(low_line_color, 60), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(low6) and not na(low7)
            // 第六条连线：low6 到 low7（不延伸）
            low_line6 := line.new(x1=low7.bar_index, y1=low7.price, x2=low6.bar_index, y2=low6.price, color=color.new(low_line_color, 75), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

        if not na(low7) and not na(low8)
            // 第七条连线：low7 到 low8（不延伸）
            low_line7 := line.new(x1=low8.bar_index, y1=low8.price, x2=low7.bar_index, y2=low7.price, color=color.new(low_line_color, 85), width=math.max(1, line_width-1), style=line.style_solid, extend=extend.none)

    // 添加所有低点标签
    if show_low_labels
        // 添加最新低点标签
        label_text = "L1: " + str.tostring(low_price, "#.##")
        new_label = label.new(low_bar_index, low_price, text=label_text, style=label.style_label_up, color=low_line_color, textcolor=color.white, size=size.small)
        array.push(low_labels, new_label)

        // 添加其他低点标签
        if not na(low2)
            label_text2 = "L2: " + str.tostring(low2.price, "#.##")
            label2 = label.new(low2.bar_index, low2.price, text=label_text2, style=label.style_label_up, color=color.new(low_line_color, 15), textcolor=color.white, size=size.small)
            array.push(low_labels, label2)

        if not na(low3)
            label_text3 = "L3: " + str.tostring(low3.price, "#.##")
            label3 = label.new(low3.bar_index, low3.price, text=label_text3, style=label.style_label_up, color=color.new(low_line_color, 30), textcolor=color.white, size=size.small)
            array.push(low_labels, label3)

        if not na(low4)
            label_text4 = "L4: " + str.tostring(low4.price, "#.##")
            label4 = label.new(low4.bar_index, low4.price, text=label_text4, style=label.style_label_up, color=color.new(low_line_color, 45), textcolor=color.white, size=size.small)
            array.push(low_labels, label4)

        if not na(low5)
            label_text5 = "L5: " + str.tostring(low5.price, "#.##")
            label5 = label.new(low5.bar_index, low5.price, text=label_text5, style=label.style_label_up, color=color.new(low_line_color, 60), textcolor=color.white, size=size.small)
            array.push(low_labels, label5)

        if not na(low6)
            label_text6 = "L6: " + str.tostring(low6.price, "#.##")
            label6 = label.new(low6.bar_index, low6.price, text=label_text6, style=label.style_label_up, color=color.new(low_line_color, 75), textcolor=color.white, size=size.small)
            array.push(low_labels, label6)

        if not na(low7)
            label_text7 = "L7: " + str.tostring(low7.price, "#.##")
            label7 = label.new(low7.bar_index, low7.price, text=label_text7, style=label.style_label_up, color=color.new(low_line_color, 85), textcolor=color.white, size=size.small)
            array.push(low_labels, label7)

        if not na(low8)
            label_text8 = "L8: " + str.tostring(low8.price, "#.##")
            label8 = label.new(low8.bar_index, low8.price, text=label_text8, style=label.style_label_up, color=color.new(low_line_color, 90), textcolor=color.white, size=size.small)
            array.push(low_labels, label8)

// ===========================
// 信息面板
// ===========================

// ===========================
// 信息面板
// ===========================

var table info_table = table.new(position.top_right, 4, 9, bgcolor=color.new(color.white, 20), border_width=1)

if barstate.islast
    table.clear(info_table, 0, 0, 3, 8)

    table.cell(info_table, 0, 0, "八个高低点连线图", text_color=color.black, bgcolor=color.new(color.gray, 50), text_size=size.small)
    table.cell(info_table, 1, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))
    table.cell(info_table, 2, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))
    table.cell(info_table, 3, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))

    // 显示八个高点
    if not na(high1)
        table.cell(info_table, 0, 1, "H1", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 1, str.tostring(high1.price, "#.##"), text_color=high_line_color, text_size=size.small)
    if not na(high2)
        table.cell(info_table, 0, 2, "H2", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 2, str.tostring(high2.price, "#.##"), text_color=color.new(high_line_color, 15), text_size=size.small)
    if not na(high3)
        table.cell(info_table, 0, 3, "H3", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 3, str.tostring(high3.price, "#.##"), text_color=color.new(high_line_color, 30), text_size=size.small)
    if not na(high4)
        table.cell(info_table, 0, 4, "H4", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 4, str.tostring(high4.price, "#.##"), text_color=color.new(high_line_color, 45), text_size=size.small)
    if not na(high5)
        table.cell(info_table, 0, 5, "H5", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 5, str.tostring(high5.price, "#.##"), text_color=color.new(high_line_color, 60), text_size=size.small)
    if not na(high6)
        table.cell(info_table, 0, 6, "H6", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 6, str.tostring(high6.price, "#.##"), text_color=color.new(high_line_color, 75), text_size=size.small)
    if not na(high7)
        table.cell(info_table, 0, 7, "H7", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 7, str.tostring(high7.price, "#.##"), text_color=color.new(high_line_color, 85), text_size=size.small)
    if not na(high8)
        table.cell(info_table, 0, 8, "H8", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 8, str.tostring(high8.price, "#.##"), text_color=color.new(high_line_color, 90), text_size=size.small)

    // 显示八个低点
    if not na(low1)
        table.cell(info_table, 2, 1, "L1", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 1, str.tostring(low1.price, "#.##"), text_color=low_line_color, text_size=size.small)
    if not na(low2)
        table.cell(info_table, 2, 2, "L2", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 2, str.tostring(low2.price, "#.##"), text_color=color.new(low_line_color, 15), text_size=size.small)
    if not na(low3)
        table.cell(info_table, 2, 3, "L3", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 3, str.tostring(low3.price, "#.##"), text_color=color.new(low_line_color, 30), text_size=size.small)
    if not na(low4)
        table.cell(info_table, 2, 4, "L4", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 4, str.tostring(low4.price, "#.##"), text_color=color.new(low_line_color, 45), text_size=size.small)
    if not na(low5)
        table.cell(info_table, 2, 5, "L5", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 5, str.tostring(low5.price, "#.##"), text_color=color.new(low_line_color, 60), text_size=size.small)
    if not na(low6)
        table.cell(info_table, 2, 6, "L6", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 6, str.tostring(low6.price, "#.##"), text_color=color.new(low_line_color, 75), text_size=size.small)
    if not na(low7)
        table.cell(info_table, 2, 7, "L7", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 7, str.tostring(low7.price, "#.##"), text_color=color.new(low_line_color, 85), text_size=size.small)
    if not na(low8)
        table.cell(info_table, 2, 8, "L8", text_color=color.black, text_size=size.small)
        table.cell(info_table, 3, 8, str.tostring(low8.price, "#.##"), text_color=color.new(low_line_color, 90), text_size=size.small)

// ===========================
// 警报
// ===========================

// ===========================
// 警报功能
// ===========================

alertcondition(not na(pivot_high), title="新高点检测", message="检测到新的高点，高点连线已更新")
alertcondition(not na(pivot_low), title="新低点检测", message="检测到新的低点，低点连线已更新")
alertcondition(not na(pivot_high) and not na(high4), title="四个高点完整", message="已收集到四个高点，高点连线图案完整")
alertcondition(not na(pivot_low) and not na(low4), title="四个低点完整", message="已收集到四个低点，低点连线图案完整")

// 趋势变化警报
if not na(high1) and not na(high2) and not na(high3)
    if high1.bar_index != high2.bar_index and high2.bar_index != high3.bar_index
        current_slope = (high1.price - high2.price) / (high1.bar_index - high2.bar_index)
        previous_slope = (high2.price - high3.price) / (high2.bar_index - high3.bar_index)

        // 检测趋势转换
        trend_change = (current_slope > 0 and previous_slope < 0) or (current_slope < 0 and previous_slope > 0)
        if trend_change
            alert("高点趋势发生转换", alert.freq_once_per_bar)

if not na(low1) and not na(low2) and not na(low3)
    if low1.bar_index != low2.bar_index and low2.bar_index != low3.bar_index
        current_slope = (low1.price - low2.price) / (low1.bar_index - low2.bar_index)
        previous_slope = (low2.price - low3.price) / (low2.bar_index - low3.bar_index)

        // 检测趋势转换
        trend_change = (current_slope > 0 and previous_slope < 0) or (current_slope < 0 and previous_slope > 0)
        if trend_change
            alert("低点趋势发生转换", alert.freq_once_per_bar)